{% extends 'base.html' %}
{% load static %}

{% block title %}{{ college.name }} Dashboard{% endblock %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-tachometer-alt"></i> {{ college.name }} Dashboard
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-university{% endblock title-icon %}

{% block title %}{{ college.name }} Dashboard{% endblock title %}

{% block subtitle %}Welcome to your college management system{% endblock subtitle %}

{% block content %}
<!-- College Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-university me-2"></i>
                    {{ college.name }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p class="mb-1"><strong>Email:</strong> {{ college.email }}</p>
                        <p class="mb-1"><strong>Phone:</strong> {{ college.phone }}</p>
                        <p class="mb-1"><strong>Address:</strong> {{ college.address|default:"Not provided" }}</p>
                        {% if subscription_expired %}
                        <div class="alert alert-warning mt-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Your subscription has expired. Please contact administrator.
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-4 text-end">
                        {% if college.logo %}
                        <img src="{{ college.logo.url }}" alt="{{ college.name }} Logo" class="img-fluid" style="max-height: 80px;">
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_students }}</h4>
                        <p class="mb-0">Active Students</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-graduate fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'student-list' %}" class="text-white text-decoration-none">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_teachers }}</h4>
                        <p class="mb-0">Teaching Staff</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chalkboard-teacher fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'staff-list' %}" class="text-white text-decoration-none">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_non_teaching }}</h4>
                        <p class="mb-0">Non-Teaching Staff</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'non-teaching-staff-list' %}" class="text-white text-decoration-none">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">₹{{ fees_collected_month|floatformat:0 }}</h4>
                        <p class="mb-0">Fees This Month</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-rupee-sign fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'fee-list' %}" class="text-white text-decoration-none">
                    View Details <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Recent Students
                </h5>
            </div>
            <div class="card-body">
                {% if recent_students %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Class</th>
                                <th>Admission Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in recent_students %}
                            <tr>
                                <td>{{ student.fullname }}</td>
                                <td>{{ student.current_class }}</td>
                                <td>{{ student.date_of_admission|date:"M d, Y" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No recent students found.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    Recent Payments
                </h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Amount</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td>{{ payment.student.fullname }}</td>
                                <td>₹{{ payment.amount }}</td>
                                <td>{{ payment.date|date:"M d, Y" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No recent payments found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Pending Fees Alert -->
{% if total_pending > 0 %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Pending Fees Alert</h5>
            <p class="mb-0">Total pending fees: <strong>₹{{ total_pending|floatformat:0 }}</strong></p>
            <a href="{% url 'fee-list' %}" class="btn btn-outline-danger btn-sm mt-2">
                View Pending Fees
            </a>
        </div>
    </div>
</div>
{% endif %}

{% endblock content %}

{% block extra_js %}
<script>
// Auto-refresh dashboard every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock extra_js %}
